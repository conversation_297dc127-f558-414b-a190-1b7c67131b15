from tortoise.models import Model
from tortoise import fields
from datetime import datetime, timedelta, date
from .recording import Recording
import uuid
from typing import Optional, List


class Appointment(Model):
    """Customer appointments"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    appointment_type = fields.CharField(max_length=10)
    appointment_date = fields.DatetimeField()
    start_time = fields.DatetimeField()
    end_time = fields.DatetimeField()
    amount = fields.DecimalField(max_digits=10, decimal_places=2)
    notes = fields.TextField(null=True)
    status = fields.Char<PERSON>ield(max_length=20, default="scheduled")
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    # Foreign key relations
    customer = fields.ForeignKeyField(
        "models.Customer", related_name="appointments", on_delete=fields.CASCADE
    )
    package = fields.ForeignKeyField(
        "models.Package",
        related_name="appointments",
        null=True,
        on_delete=fields.CASCADE,
    )
    service = fields.ForeignKeyField(
        "models.Service",
        related_name="appointments",
        null=True,
        on_delete=fields.CASCADE,
    )
    staff = fields.ForeignKeyField(
        "models.Staff", related_name="appointments", on_delete=fields.CASCADE
    )

    # Reverse relations
    recordings: fields.ReverseRelation["Recording"]
    appointment_segments = fields.ReverseRelation["AppointmentSegment"]

    class Meta:
        table = "appointments"

    @classmethod
    async def check_staff_availability(
        cls, staff_id: uuid.UUID, start_time: datetime, end_time: datetime
    ) -> bool:
        """Check if staff is available - Prisma-like"""
        conflicting = await cls.filter(
            staff_id=staff_id,
            status__in=["scheduled", "confirmed", "in_progress"],
            start_time__lt=end_time,
            end_time__gt=start_time,
        ).exists()
        return not conflicting

    def __str__(self):
        return f"(Appointment_id={self.id}, date={self.appointment_date.date()}, start_time={self.start_time.time()}, status={self.status} )"

    @classmethod
    async def get_prev_appointments(cls, customer_id: str):
        appointments = (
            await Appointment.filter(customer_id=customer_id)
            .prefetch_related("recordings", "staff", "service", "package", "customer", "appointment_segments")
            .order_by("-appointment_date")  # Sort by latest appointments first
            .limit(5)
        )

        return appointments

    @classmethod
    async def get_appointment_staff(cls, appointment_id: str):
        appointments = (
            await Appointment.filter(id=appointment_id)
            .prefetch_related("staff")
            .first()
        )

        return appointments

    @classmethod
    async def create_appointment(cls, **data) -> "Appointment":
        """Create new appointment with validation"""
        # Validate staff availability
        # if not await cls.check_staff_availability(
        #     data["staff_id"], data["start_time"], data["end_time"]
        # ):
        #     raise ValueError("Staff not available at requested time")
        return await cls.create(**data)

    @classmethod
    def calculate_end_time(cls, start_time: datetime, duration_minutes: int) -> datetime:
        """Calculate end time based on start time and duration"""
        return start_time + timedelta(minutes=duration_minutes)

    @classmethod
    def spans_multiple_days(cls, start_time: datetime, end_time: datetime) -> bool:
        """Check if appointment spans multiple days (utility method for reference)"""
        return start_time.date() != end_time.date()

    @classmethod
    async def create_appointment_segments(cls, appointment: "Appointment", start_time: datetime, end_time: datetime) -> list:
        """Create appointment segments for all appointments (single or multi-day)"""
        segments = []
        current_start = start_time
        segment_order = 1

        while current_start < end_time:
            # Calculate end of current day (23:59:59)
            current_day_end = current_start.replace(hour=23, minute=59, second=59, microsecond=999999)

            # Determine segment end time
            segment_end = min(end_time, current_day_end)

            # Calculate duration for this segment
            segment_duration = int((segment_end - current_start).total_seconds() / 60)

            # Ensure minimum duration of 1 minute
            if segment_duration < 1:
                segment_duration = 1

            # Create segment
            segment = await AppointmentSegment.create(
                appointment=appointment,
                segment_date=current_start.date(),
                start_datetime=current_start,
                end_datetime=segment_end,
                segment_order=segment_order,
                duration_minutes=segment_duration,
                is_first_segment=(segment_order == 1),
                is_last_segment=(segment_end >= end_time)
            )
            segments.append(segment)

            # Move to next day if appointment continues
            if segment_end < end_time:
                next_day = current_start.date() + timedelta(days=1)
                current_start = datetime.combine(next_day, datetime.min.time()).replace(tzinfo=current_start.tzinfo)
                segment_order += 1
            else:
                break

        return segments

    @classmethod
    async def create_appointment_with_segments(cls, **data) -> "Appointment":
        """Create appointment and handle segmentation for multi-day appointments"""
        # Extract timing information
        start_time = data.get('appointment_date')

        if not start_time:
            raise ValueError("appointment_date is required")

        # Calculate duration from service or package
        duration_minutes = 0
        if data.get('service_id'):
            from .service import Service
            service = await Service.get(id=data['service_id'])
            duration_minutes = service.duration_minutes
        elif data.get('package_id'):
            from .service import Package
            package = await Package.get(id=data['package_id'])
            duration_minutes = package.total_duration_minutes or 60  # Default to 1 hour if not set
        else:
            duration_minutes = 60  # Default duration

        end_time = cls.calculate_end_time(start_time, duration_minutes)

        # Update data with calculated times
        data['start_time'] = start_time
        data['end_time'] = end_time

        # Create the appointment
        appointment = await cls.create(**data)

        # Always create segments for all appointments (single or multi-day)
        await cls.create_appointment_segments(appointment, start_time, end_time)

        return appointment

    async def get_segments(self):
        """Get all segments for this appointment"""
        return await AppointmentSegment.get_appointment_segments(self.id)

    async def get_total_duration_from_segments(self) -> int:
        """Get total duration by summing all segments"""
        segments = await self.get_segments()
        return sum(segment.duration_minutes for segment in segments)

class AppointmentSegment(Model):
    """Customer appointment segments for each appointment"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    segment_date = fields.DateField()
    start_datetime = fields.DatetimeField()
    end_datetime = fields.DatetimeField()
    segment_order = fields.IntField()
    duration_minutes = fields.IntField()
    is_first_segment = fields.BooleanField(default=False)
    is_last_segment = fields.BooleanField(default=False)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    # Foreign key relations
    appointment = fields.ForeignKeyField(
        "models.Appointment", related_name="appointment_segments", on_delete=fields.CASCADE
    )

    class Meta:
        table = "appointment_segments"

    def __str__(self):
        return f"Segment {self.segment_order} for Appointment {self.appointment.id if self.appointment else 'Unknown'} on {self.segment_date}"

    @classmethod
    async def get_appointment_segments(cls, appointment_id: uuid.UUID):
        """Get all segments for a specific appointment ordered by segment_order"""
        return await cls.filter(appointment_id=appointment_id).order_by('segment_order')

    @classmethod
    async def get_segments_by_date(cls, segment_date: date):
        """Get all appointment segments for a specific date"""
        return await cls.filter(segment_date=segment_date).prefetch_related('appointment', 'appointment__customer', 'appointment__staff')

    @classmethod
    async def get_segments_by_date_range(cls, start_date: date, end_date: date):
        """Get all appointment segments within a date range"""
        return await cls.filter(
            segment_date__gte=start_date,
            segment_date__lte=end_date
        ).prefetch_related('appointment', 'appointment__customer', 'appointment__staff').order_by('segment_date', 'start_datetime')

    @classmethod
    async def check_segment_conflicts(cls, start_datetime: datetime, end_datetime: datetime, staff_id: uuid.UUID, exclude_appointment_id: Optional[uuid.UUID] = None) -> bool:
        """Check if there are any conflicting segments for a staff member at the given time"""
        query = cls.filter(
            appointment__staff_id=staff_id,
            appointment__status__in=["scheduled", "confirmed", "in_progress"],
            start_datetime__lt=end_datetime,
            end_datetime__gt=start_datetime,
        )

        if exclude_appointment_id:
            query = query.exclude(appointment_id=exclude_appointment_id)

        return await query.exists()

    async def get_total_appointment_duration(self) -> int:
        """Get the total duration of the parent appointment across all segments"""
        segments = await AppointmentSegment.get_appointment_segments(self.appointment.id)
        return sum(segment.duration_minutes for segment in segments)

    async def is_appointment_complete(self) -> bool:
        """Check if all segments of the appointment are completed"""
        # This would require adding a status field to segments or checking the parent appointment status
        # For now, we'll check the parent appointment status
        if not hasattr(self, 'appointment') or not self.appointment:
            await self.fetch_related('appointment')
        return self.appointment.status == "completed"

    @classmethod
    async def delete_appointment_segments(cls, appointment_id: uuid.UUID) -> int:
        """Delete all segments for a specific appointment"""
        return await cls.filter(appointment_id=appointment_id).delete()

    @classmethod
    async def update_segment_times(cls, segment_id: uuid.UUID, new_start: datetime, new_end: datetime) -> bool:
        """Update the start and end times for a specific segment"""
        try:
            segment = await cls.get(id=segment_id)
            segment.start_datetime = new_start
            segment.end_datetime = new_end
            segment.duration_minutes = int((new_end - new_start).total_seconds() / 60)
            await segment.save()
            return True
        except Exception:
            return False

