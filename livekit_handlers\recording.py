from livekit.agents import JobContext
from livekit import api
from livekit.agents.voice import AgentSession
import json
from livekit.agents import BackgroundAudioPlayer, AudioConfig, BuiltinAudioClip
from livekit.rtc import room
from openai import OpenAI
from config.settings import settings, agent_settings
from config.assistant import CONVERSATION_SUMMARY_AGENT_PROMPT
from shared_types.context_variables import ContextVariables
from models import Recording
import uuid
from urllib.parse import quote


class RoomRecodingAndTranscript:
    def __init__(
        self, ctx: JobContext, session: AgentSession, ctx_vars: ContextVariables
    ) -> None:
        self.session = session
        self.ctx = ctx
        self.ctx_vars = ctx_vars
        self.room_name = ctx.room.name
        self.llm_client = OpenAI(api_key=settings.OPENAI_API_KEY)

    async def make_egress_req(self):
        print("🎙️ Started recording session")
        async with api.LiveKitAPI() as lkapi:
            room_req = api.RoomCompositeEgressRequest(
                room_name=self.room_name,
                audio_only=True,
                file_outputs=[
                    api.EncodedFileOutput(
                        file_type=api.EncodedFileType.OGG,
                        filepath=settings.RECORDINGS_PATH.format(room_name=self.room_name),
                        s3=api.S3Upload(
                            bucket=settings.AWS_BUCKET_NAME,
                            region=settings.AWS_REGION,
                            access_key=settings.AWS_ACCESS_KEY_ID,
                            secret=settings.AWS_SECRET_ACCESS_KEY,
                        ),
                    )
                ],
            )

        # track_req = api.TrackCompositeEgressRequest(
        #     room_name=self.room_name,
        #     audio_track_id='1',
        #     # filepath="{room_name}/individual_tracks/track_{publisher_identity}_{time}",
        #     file_outputs=[
        #         api.EncodedFileOutput(
        #             file_type=api.EncodedFileType.OGG,
        #             filepath=f"tracks/{self.room_name}/recording.ogg",
        #             s3=api.S3Upload(
        #                 bucket=settings.AWS_BUCKET_NAME,
        #                 region=settings.AWS_REGION,
        #                 access_key=settings.AWS_ACCESS_KEY_ID,
        #                 secret=settings.AWS_SECRET_ACCESS_KEY,
        #             ),
        #         )
        #     ],
        # )

            await lkapi.egress.start_room_composite_egress(room_req)
            # await lkapi.egress.start_track_composite_egress(track_req)

    def is_valid_uuid(self, val: str) -> bool:
        try:
            uuid_obj = uuid.UUID(val)
            return True
        except (ValueError, AttributeError, TypeError):
            return False

    async def write_transcript(self, is_appointment_created: bool, customer_id: str, duration: float):
        print("✅✅ Appointment created -> ", is_appointment_created)
        conversation_history = self.session.history.to_dict()
        summary = await self.summarize_conversation(conversation_history)

        await Recording.create_recording(
            id=self.ctx_vars["recording_id"],
            customer_id=customer_id,
            appointment_id=(
                self.ctx_vars["appointment_id"] if is_appointment_created else None
            ),
            room_id=(
                self.ctx.room.name if self.is_valid_uuid(self.ctx.room.name) else None
            ),
            file_path=f'{settings.RECORDINGS_PATH.format(room_name=quote(self.room_name, safe=""))}',
            purpose="TESTING PURPOSE",
            transcript=json.dumps(conversation_history),
            summary=summary,
            duration_minutes=duration,
        )

    async def turn_on_background_audio(self):
        if settings.AGENT_BACKGROUND_AUDIO:
            background_audio = BackgroundAudioPlayer(
                ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.8),
                thinking_sound=[
                    AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING, volume=0.8),
                    AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING2, volume=0.7),
                ],
            )

            await background_audio.start(room=self.ctx.room, agent_session=self.session)
        else:
            return None

    def preprocess_conversation(self, conversation_items):
        transcript_lines = []

        for item in conversation_items:
            if item["type"] != "message":
                continue
            if not item.get("content") or not item["content"][0].strip():
                continue

            role = item["role"]
            speaker = "Assistant" if role == "assistant" else "User"
            message = " ".join(item["content"]).strip()
            transcript_lines.append(f"{speaker}: {message}")

        return "\n".join(transcript_lines)

    async def summarize_conversation(self, conversation_json):
        transcript = self.preprocess_conversation(conversation_json.get("items"))

        prompt = f"{transcript}"

        response = self.llm_client.chat.completions.create(
            model=agent_settings.LLM_SUMMARY_MODEL,
            messages=[
                {
                    "role": "system",
                    "content": CONVERSATION_SUMMARY_AGENT_PROMPT,
                },
                {"role": "user", "content": prompt},
            ],
            temperature=0.5,
        )

        summary = response.choices[0].message.content

        return summary
