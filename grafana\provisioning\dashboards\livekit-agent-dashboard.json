{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "enable": true, "expr": "changes(livekit_active_sessions[1m])", "hide": false, "iconColor": "green", "name": "Session Changes"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 4, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "panels": [], "title": "Overview", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 1}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "increase(livekit_stt_requests_total[5m])", "legendFormat": "STT", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "increase(livekit_tts_requests_total[5m])", "legendFormat": "TTS", "refId": "B"}], "title": "Total Requests (5m)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 2}, {"color": "red", "value": 5}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 1}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_conversation_latency_seconds_sum[5m]) / rate(livekit_conversation_latency_seconds_count[5m])", "legendFormat": "Avg Latency", "refId": "A"}], "title": "Average Conversation Latency", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "yellow", "value": 3}, {"color": "red", "value": 8}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 1}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "histogram_quantile(0.95, rate(livekit_conversation_latency_seconds_bucket[5m]))", "legendFormat": "P95 Latency", "refId": "A"}], "title": "P95 Conversation Latency", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 6, "panels": [], "title": "Conversation Latency", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_conversation_latency_seconds_sum[1m]) / rate(livekit_conversation_latency_seconds_count[1m])", "legendFormat": "Average Latency", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "histogram_quantile(0.95, rate(livekit_conversation_latency_seconds_bucket[1m]))", "legendFormat": "P95 Latency", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "histogram_quantile(0.99, rate(livekit_conversation_latency_seconds_bucket[1m]))", "legendFormat": "P99 Latency", "refId": "C"}], "title": "Conversation Latency Over Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_eou_end_of_utterance_delay_seconds_sum[1m]) / rate(livekit_eou_end_of_utterance_delay_seconds_count[1m])", "legendFormat": "EOU Delay", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_llm_ttft_seconds_sum[1m]) / rate(livekit_llm_ttft_seconds_count[1m])", "legendFormat": "LLM TTFT", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_tts_ttfb_seconds_sum[1m]) / rate(livekit_tts_ttfb_seconds_count[1m])", "legendFormat": "TTS TTFB", "refId": "C"}], "title": "Latency Component Breakdown", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 9, "panels": [], "title": "LLM Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 15}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_llm_duration_seconds_sum[1m]) / rate(livekit_llm_duration_seconds_count[1m])", "legendFormat": "Average Duration", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "histogram_quantile(0.95, rate(livekit_llm_duration_seconds_bucket[1m]))", "legendFormat": "P95 Duration", "refId": "B"}], "title": "LLM Processing Time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 15}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_llm_prompt_tokens_sum[1m]) / rate(livekit_llm_prompt_tokens_count[1m])", "legendFormat": "Avg Prompt Tokens", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_llm_completion_tokens_sum[1m]) / rate(livekit_llm_completion_tokens_count[1m])", "legendFormat": "Avg Completion Tokens", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_llm_total_tokens_sum[1m]) / rate(livekit_llm_total_tokens_count[1m])", "legendFormat": "Avg Total Tokens", "refId": "C"}], "title": "LLM Token Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time to First Token"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 15}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_llm_tokens_per_second_sum[1m]) / rate(livekit_llm_tokens_per_second_count[1m])", "legendFormat": "Tokens/Second", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "rate(livekit_llm_ttft_seconds_sum[1m]) / rate(livekit_llm_ttft_seconds_count[1m])", "legendFormat": "Time to First Token", "refId": "B"}], "title": "LLM Performance", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 41, "tags": ["livekit", "agents", "ai", "metrics"], "templating": {"list": [{"current": {"text": "prometheus", "value": "ceqsxcm418idcb"}, "includeAll": false, "label": "Datasource", "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(livekit_active_sessions, job)", "includeAll": true, "label": "Job", "multi": true, "name": "job", "options": [], "query": {"query": "label_values(livekit_active_sessions, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(livekit_active_sessions{job=~\"$job\"}, instance)", "includeAll": true, "label": "Instance", "multi": true, "name": "instance", "options": [], "query": {"query": "label_values(livekit_active_sessions{job=~\"$job\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "LiveKit Agent Metrics", "uid": "livekit-agent-metrics", "version": 2}