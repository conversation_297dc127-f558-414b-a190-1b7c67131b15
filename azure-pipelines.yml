trigger:
  branches:
    include:
      - release/dev

resources:
- repo: self

variables:
- group: voice-agent
- group: deployment-secrets
- name: awsRegion
  value: 'us-east-1'
- name: ec2User
  value: 'ubuntu'
- name: imageTag
  value: '$(Build.Repository.Name)-$(Build.BuildId)-$(Build.SourceBranchName)'
- name: dockerfilePath
  value: 'Dockerfile'
- name: composeFile
  value: 'docker-compose.yml'
- name: remoteDir
  value: 'back-voice-agent'

stages:

# 🧪 Stage 1: Validation
- stage: Init
  displayName: Initialization and Validation
  jobs:
  - job: Validate
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - script: |
        echo "=== Validating Environment ==="
        [ -z "$(AWS_ACCESS_KEY_ID)" ] && echo "Missing AWS_ACCESS_KEY_ID" && exit 1
        [ -z "$(AWS_SECRET_ACCESS_KEY)" ] && echo "Missing AWS_SECRET_ACCESS_KEY" && exit 1
        [ -z "$(ECR_REPOSITORY_URL)" ] && echo "Missing ECR_REPOSITORY_URL" && exit 1
        [ -z "$(EC2_INSTANCE_IP)" ] && echo "Missing EC2_INSTANCE_IP" && exit 1
        echo "Validation complete."
      displayName: Environment Check

# 🛠 Stage 2: Build & Push
- stage: BuildAndPush
  displayName: Build and Push Docker Image
  dependsOn: Init
  jobs:
  - job: DockerBuildPush
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - script: ls -la $(System.DefaultWorkingDirectory)/$(dockerfilePath)
      displayName: Verify Dockerfile Presence

    - task: DockerInstaller@0
      inputs:
        dockerVersion: '20.10.11'
      displayName: Install Docker

    - script: |
        curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
        unzip awscliv2.zip
        sudo ./aws/install --update

        aws configure set aws_access_key_id "$(AWS_ACCESS_KEY_ID)"
        aws configure set aws_secret_access_key "$(AWS_SECRET_ACCESS_KEY)"
        aws configure set default.region "$(awsRegion)"

        echo "Logging into ECR..."
        aws ecr get-login-password --region "$(awsRegion)" | docker login --username AWS --password-stdin "$(ECR_REPOSITORY_URL)"
      displayName: Configure AWS and ECR Login
      env:
        AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
        AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)
        ECR_REPOSITORY_URL: $(ECR_REPOSITORY_URL)

    - task: Docker@2
      inputs:
        command: build
        repository: $(ECR_REPOSITORY_URL)
        dockerfile: '$(System.DefaultWorkingDirectory)/$(dockerfilePath)'
        buildContext: '$(System.DefaultWorkingDirectory)'
        tags: |
          $(imageTag)
      displayName: Build Docker Image

    - script: |
        docker push $(ECR_REPOSITORY_URL):$(imageTag)
        echo "##vso[task.setvariable variable=BUILT_IMAGE;isOutput=true]$(ECR_REPOSITORY_URL):$(imageTag)"
      displayName: Push Docker Image
      env:
        ECR_REPOSITORY_URL: $(ECR_REPOSITORY_URL)

# 🚀 Stage 3: Deploy
- stage: Deploy
  displayName: Deploy to Ubuntu EC2
  dependsOn: BuildAndPush
  jobs:
  - job: DeployToEC2
    pool:
      vmImage: 'ubuntu-latest'
    steps:
    - task: DownloadSecureFile@1
      name: ec2KeyPair
      inputs:
        secureFile: 'AI Voice Agent_key-pair.pem'

    - script: |
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        cp "$(ec2KeyPair.secureFilePath)" ~/.ssh/ec2-key.pem
        chmod 600 ~/.ssh/ec2-key.pem
        ssh-keyscan "$(EC2_INSTANCE_IP)" >> ~/.ssh/known_hosts

        ssh -i ~/.ssh/ec2-key.pem $(ec2User)@$(EC2_INSTANCE_IP) "mkdir -p ~/$(remoteDir)/grafana/provisioning"

        echo "Copying necessary files to EC2..."
        # Copy compose file
        scp -i ~/.ssh/ec2-key.pem $(System.DefaultWorkingDirectory)/docker-compose.yml $(ec2User)@$(EC2_INSTANCE_IP):~/$(remoteDir)/
        
        # Copy supporting files
        if [ -f "$(System.DefaultWorkingDirectory)/.env" ]; then
          scp -i ~/.ssh/ec2-key.pem $(System.DefaultWorkingDirectory)/.env $(ec2User)@$(EC2_INSTANCE_IP):~/$(remoteDir)/
        fi
        if [ -f "$(System.DefaultWorkingDirectory)/prometheus.yml" ]; then
          scp -i ~/.ssh/ec2-key.pem $(System.DefaultWorkingDirectory)/prometheus.yml $(ec2User)@$(EC2_INSTANCE_IP):~/$(remoteDir)/
        fi
        if [ -d "$(System.DefaultWorkingDirectory)/grafana/provisioning" ]; then
          scp -r -i ~/.ssh/ec2-key.pem $(System.DefaultWorkingDirectory)/grafana/provisioning $(ec2User)@$(EC2_INSTANCE_IP):~/$(remoteDir)/grafana/
        fi
      displayName: Provision EC2
      env:
        EC2_INSTANCE_IP: $(EC2_INSTANCE_IP)

    - script: |
        ssh -i ~/.ssh/ec2-key.pem $(ec2User)@$(EC2_INSTANCE_IP) << 'DEPLOY'
        set -ex
        cd ~/back-voice-agent
        echo ECR_IMAGE=$(ECR_REPOSITORY_URL):$(imageTag) > .env

        # Clean up disk space first
        docker system prune -af || true
        sudo journalctl --vacuum-size=100M || true
        sudo find /var/log -type f -name "*.log" -exec truncate -s 0 {} \; || true

        # Create required directories
        mkdir -p recordings logs grafana/provisioning

        # Set up AWS CLI if not present
        if ! command -v aws &> /dev/null; then
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          sudo ./aws/install --update
        fi

        # Configure AWS access
        aws configure set aws_access_key_id "$(AWS_ACCESS_KEY_ID)"
        aws configure set aws_secret_access_key "$(AWS_SECRET_ACCESS_KEY)"
        aws configure set default.region "$(awsRegion)"
        
        # Login to ECR with verification
        if ! aws ecr get-login-password --region "$(awsRegion)" | sudo docker login --username AWS --password-stdin "$(ECR_REPOSITORY_URL)"; then
          echo "##[error]Failed to login to ECR"
          exit 1
        fi

        # Verify we can pull the image first
        if ! sudo docker pull "$(ECR_REPOSITORY_URL):$(imageTag)"; then
          echo "##[error]Failed to pull image from ECR"
          exit 1
        fi

        # Deploy with the pre-built image
        export ECR_IMAGE="$(ECR_REPOSITORY_URL):$(imageTag)"
        sudo docker-compose pull
        sudo docker-compose down || true
        sudo docker-compose up -d --remove-orphans
        
        # Verify services are running
        sudo docker ps --filter "name=salon_" --format "table {{.Names}}\t{{.Status}}"
        DEPLOY
      displayName: Deploy Container
      env:
        AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
        AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)
        EC2_INSTANCE_IP: $(EC2_INSTANCE_IP)
        ECR_REPOSITORY_URL: $(ECR_REPOSITORY_URL)