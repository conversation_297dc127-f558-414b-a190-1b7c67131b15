from typing import Any
from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server
from livekit.agents import metrics
from config.settings import settings

class PrometheusExporter:
    # Class variables to track if metrics have been registered
    _metrics_registered = False
    _server_started = False
    
    # Class variables to store the metrics objects
    _stt_audio_duration = None
    _stt_processing_duration = None
    _stt_requests_total = None
    _llm_duration = None
    _llm_ttft = None
    _llm_tokens_per_second = None
    _llm_completion_tokens = None
    _llm_prompt_tokens = None
    _llm_total_tokens = None
    _llm_cached_tokens = None
    _tts_audio_duration = None
    _tts_processing_duration = None
    _tts_ttfb = None
    _tts_characters_count = None
    _tts_requests_total = None
    _eou_end_of_utterance_delay = None
    _eou_transcription_delay = None
    _eou_callback_delay = None
    _conversation_latency = None
    _session_info = None
    _active_sessions = None
    
    def __init__(self):
        self.port = settings.PROMETHEUS_PORT
        
        # Only set up metrics if they haven't been registered yet
        if not PrometheusExporter._metrics_registered:
            self._setup_prometheus_metrics()
            PrometheusExporter._metrics_registered = True
        
        # Always assign the class metrics to instance variables
        self.stt_audio_duration = PrometheusExporter._stt_audio_duration
        self.stt_processing_duration = PrometheusExporter._stt_processing_duration
        self.stt_requests_total = PrometheusExporter._stt_requests_total
        self.llm_duration = PrometheusExporter._llm_duration
        self.llm_ttft = PrometheusExporter._llm_ttft
        self.llm_tokens_per_second = PrometheusExporter._llm_tokens_per_second
        self.llm_completion_tokens = PrometheusExporter._llm_completion_tokens
        self.llm_prompt_tokens = PrometheusExporter._llm_prompt_tokens
        self.llm_total_tokens = PrometheusExporter._llm_total_tokens
        self.llm_cached_tokens = PrometheusExporter._llm_cached_tokens
        self.tts_audio_duration = PrometheusExporter._tts_audio_duration
        self.tts_processing_duration = PrometheusExporter._tts_processing_duration
        self.tts_ttfb = PrometheusExporter._tts_ttfb
        self.tts_characters_count = PrometheusExporter._tts_characters_count
        self.tts_requests_total = PrometheusExporter._tts_requests_total
        self.eou_end_of_utterance_delay = PrometheusExporter._eou_end_of_utterance_delay
        self.eou_transcription_delay = PrometheusExporter._eou_transcription_delay
        self.eou_callback_delay = PrometheusExporter._eou_callback_delay
        self.conversation_latency = PrometheusExporter._conversation_latency
        self.session_info = PrometheusExporter._session_info
        self.active_sessions = PrometheusExporter._active_sessions
    
    def _setup_prometheus_metrics(self):
        """Initialize Prometheus metrics"""
        
        # STT Metrics
        PrometheusExporter._stt_audio_duration = Histogram(
            'livekit_stt_audio_duration_seconds',
            'Duration of audio input received by STT model',
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, float('inf')]
        )
        
        PrometheusExporter._stt_processing_duration = Histogram(
            'livekit_stt_processing_duration_seconds',
            'Time taken to create transcript (non-streaming STT)',
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, float('inf')]
        )
        
        PrometheusExporter._stt_requests_total = Counter(
            'livekit_stt_requests_total',
            'Total STT requests',
            ['streamed']
        )
        
        # LLM Metrics
        PrometheusExporter._llm_duration = Histogram(
            'livekit_llm_duration_seconds',
            'Time taken for LLM to generate completion',
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, float('inf')]
        )
        
        PrometheusExporter._llm_ttft = Histogram(
            'livekit_llm_ttft_seconds',
            'Time to first token from LLM',
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, float('inf')]
        )
        
        PrometheusExporter._llm_tokens_per_second = Histogram(
            'livekit_llm_tokens_per_second',
            'Rate of token generation by LLM',
            buckets=[1, 5, 10, 20, 50, 100, 200, float('inf')]
        )
        
        PrometheusExporter._llm_completion_tokens = Histogram(
            'livekit_llm_completion_tokens',
            'Number of tokens generated by LLM',
            buckets=[10, 50, 100, 200, 500, 1000, 2000, float('inf')]
        )
        
        PrometheusExporter._llm_prompt_tokens = Histogram(
            'livekit_llm_prompt_tokens',
            'Number of tokens in prompt',
            buckets=[10, 50, 100, 200, 500, 1000, 2000, float('inf')]
        )
        
        PrometheusExporter._llm_total_tokens = Histogram(
            'livekit_llm_total_tokens',
            'Total token usage for completion',
            buckets=[20, 100, 200, 400, 1000, 2000, 4000, float('inf')]
        )
        
        PrometheusExporter._llm_cached_tokens = Histogram(
            'livekit_llm_cached_tokens',
            'Number of cached tokens in prompt',
            buckets=[0, 10, 50, 100, 200, 500, 1000, float('inf')]
        )
        
        # TTS Metrics
        PrometheusExporter._tts_audio_duration = Histogram(
            'livekit_tts_audio_duration_seconds',
            'Duration of audio output generated by TTS',
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, float('inf')]
        )
        
        PrometheusExporter._tts_processing_duration = Histogram(
            'livekit_tts_processing_duration_seconds',
            'Time taken for TTS to generate audio',
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, float('inf')]
        )
        
        PrometheusExporter._tts_ttfb = Histogram(
            'livekit_tts_ttfb_seconds',
            'Time to first byte of TTS audio output',
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, float('inf')]
        )
        
        PrometheusExporter._tts_characters_count = Histogram(
            'livekit_tts_characters_count',
            'Number of characters in TTS input text',
            buckets=[10, 50, 100, 200, 500, 1000, 2000, float('inf')]
        )
        
        PrometheusExporter._tts_requests_total = Counter(
            'livekit_tts_requests_total',
            'Total TTS requests',
            ['streamed']
        )
        
        # EOU Metrics
        PrometheusExporter._eou_end_of_utterance_delay = Histogram(
            'livekit_eou_end_of_utterance_delay_seconds',
            'Time from end of speech to turn completion',
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, float('inf')]
        )
        
        PrometheusExporter._eou_transcription_delay = Histogram(
            'livekit_eou_transcription_delay_seconds',
            'Time between end of speech and final transcript',
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, float('inf')]
        )
        
        PrometheusExporter._eou_callback_delay = Histogram(
            'livekit_eou_callback_delay_seconds',
            'Time taken to execute on_user_turn_completed callback',
            buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, float('inf')]
        )
        
        # Conversation Latency
        PrometheusExporter._conversation_latency = Histogram(
            'livekit_conversation_latency_seconds',
            'Total conversation latency (EOU delay + LLM TTFT + TTS TTFB)',
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, float('inf')]
        )
        
        # Session Info
        PrometheusExporter._session_info = Info(
            'livekit_session_info',
            'Information about the current session'
        )
        
        # Active sessions gauge
        PrometheusExporter._active_sessions = Gauge(
            'livekit_active_sessions',
            'Number of active agent sessions'
        )
        
        # Mark metrics as registered
        PrometheusExporter._metrics_registered = True
        
    def start_metrics_server(self):
        # Only start the server if it hasn't been started yet
        if not PrometheusExporter._server_started:
            try:
                start_http_server(self.port)
                PrometheusExporter._server_started = True
                print(f"Prometheus metrics server started on port {self.port}")
            except Exception as e:
                print(f"Failed to start Prometheus metrics server: {e}")
        
    def export_stt_metrics(self, stt_metrics: metrics.STTMetrics):
        """Export STT metrics to Prometheus"""
        self.stt_audio_duration.observe(stt_metrics.audio_duration)
        self.stt_processing_duration.observe(stt_metrics.duration)
        self.stt_requests_total.labels(streamed=str(stt_metrics.streamed)).inc()
        
    def export_llm_metrics(self, llm_metrics: metrics.LLMMetrics):
        """Export LLM metrics to Prometheus"""
        self.llm_duration.observe(llm_metrics.duration)
        self.llm_ttft.observe(llm_metrics.ttft)
        self.llm_tokens_per_second.observe(llm_metrics.tokens_per_second)
        self.llm_completion_tokens.observe(llm_metrics.completion_tokens)
        self.llm_prompt_tokens.observe(llm_metrics.prompt_tokens)
        self.llm_total_tokens.observe(llm_metrics.total_tokens)
        
        if hasattr(llm_metrics, 'prompt_cached_tokens') and llm_metrics.prompt_cached_tokens:
            self.llm_cached_tokens.observe(llm_metrics.prompt_cached_tokens)
            
    def export_tts_metrics(self, tts_metrics: metrics.TTSMetrics):
        """Export TTS metrics to Prometheus"""
        self.tts_audio_duration.observe(tts_metrics.audio_duration)
        self.tts_processing_duration.observe(tts_metrics.duration)
        self.tts_ttfb.observe(tts_metrics.ttfb)
        self.tts_characters_count.observe(tts_metrics.characters_count)
        self.tts_requests_total.labels(streamed=str(tts_metrics.streamed)).inc()
        
    def export_eou_metrics(self, eou_metrics: metrics.EOUMetrics):
        """Export EOU metrics to Prometheus"""
        self.eou_end_of_utterance_delay.observe(eou_metrics.end_of_utterance_delay)
        self.eou_transcription_delay.observe(eou_metrics.transcription_delay)
        self.eou_callback_delay.observe(eou_metrics.on_user_turn_completed_delay)
        
    def calculate_conversation_latency(self, eou_metrics: metrics.EOUMetrics, 
                                     llm_metrics: metrics.LLMMetrics, 
                                     tts_metrics: metrics.TTSMetrics):
        """Calculate and export total conversation latency"""
        total_latency = eou_metrics.end_of_utterance_delay + llm_metrics.ttft + tts_metrics.ttfb
        self.conversation_latency.observe(total_latency)
        return total_latency
        
    def export_metrics(self, metric_obj: Any):
        """Export metrics based on type"""
        if isinstance(metric_obj, metrics.STTMetrics):
            self.export_stt_metrics(metric_obj)
        elif isinstance(metric_obj, metrics.LLMMetrics):
            self.export_llm_metrics(metric_obj)
        elif isinstance(metric_obj, metrics.TTSMetrics):
            self.export_tts_metrics(metric_obj)
        elif isinstance(metric_obj, metrics.EOUMetrics):
            self.export_eou_metrics(metric_obj)
            
    def increment_active_sessions(self):
        """Increment active sessions counter"""
        self.active_sessions.inc()
        
    def decrement_active_sessions(self):
        """Decrement active sessions counter"""
        self.active_sessions.dec()
        
    def set_session_info(self, session_id: str, **kwargs):
        """Set session information"""
        info_dict = {'session_id': session_id}
        info_dict.update(kwargs)
        self.session_info.info(info_dict)
