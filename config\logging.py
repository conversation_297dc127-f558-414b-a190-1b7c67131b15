import logging
import sys
import os
from pythonjsonlogger import jsonlogger
from .settings import settings


class StructuredFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter for structured logging compatible with Loki"""

    def add_fields(self, log_record, record, message_dict):
        super(StructuredFormatter, self).add_fields(log_record, record, message_dict)
        log_record['timestamp'] = record.created
        log_record['level'] = record.levelname
        log_record['logger'] = record.name
        log_record['module'] = record.module
        log_record['function'] = record.funcName
        log_record['line'] = record.lineno

        # Add application-specific fields
        log_record['service'] = 'salon-ai-voice-agent'
        log_record['environment'] = getattr(settings, 'ENVIRONMENT', 'development')


def setup_logging():
    """Setup logging with both standard and structured formats"""

    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)

    # Standard formatter for console output
    standard_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # JSON formatter for file output (Loki-compatible)
    json_formatter = StructuredFormatter(
        '%(timestamp)s %(level)s %(logger)s %(module)s %(function)s %(line)s %(message)s'
    )

    # Console handler with standard format
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(standard_formatter)

    # File handler with standard format (for backward compatibility)
    file_handler = logging.FileHandler("salon_ai.log")
    file_handler.setFormatter(standard_formatter)

    # Structured JSON file handler for Loki
    json_file_handler = logging.FileHandler("logs/salon_ai_structured.log")
    json_file_handler.setFormatter(json_formatter)

    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, settings.LOG_LEVEL),
        handlers=[
            console_handler,
            file_handler,
            json_file_handler,
        ],
    )

    # Add specific loggers for different components
    setup_component_loggers()


def setup_component_loggers():
    """Setup specific loggers for different application components"""

    # Voice agent logger
    voice_logger = logging.getLogger('salon_ai.voice_agent')
    voice_logger.setLevel(getattr(logging, settings.LOG_LEVEL))

    # Database logger
    db_logger = logging.getLogger('salon_ai.database')
    db_logger.setLevel(getattr(logging, settings.LOG_LEVEL))

    # LiveKit logger
    livekit_logger = logging.getLogger('salon_ai.livekit')
    livekit_logger.setLevel(getattr(logging, settings.LOG_LEVEL))

    # Metrics logger
    metrics_logger = logging.getLogger('salon_ai.metrics')
    metrics_logger.setLevel(getattr(logging, settings.LOG_LEVEL))


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the salon_ai prefix"""
    return logging.getLogger(f'salon_ai.{name}')
